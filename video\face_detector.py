"""
人脸检测模块
将原face-api.js的人脸检测功能转换为Python
使用OpenCV和dlib进行人脸检测
"""
import cv2
import os
import numpy as np
import subprocess
import sys
import tempfile
from typing import List, Tuple, Optional
from pathlib import Path
import threading
import time


class FaceDetector:
    """人脸检测器"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe"):
        """
        初始化人脸检测器

        Args:
            ffmpeg_path: FFmpeg可执行文件路径
            ffprobe_path: FFprobe可执行文件路径
        """
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.face_cascade = None
        self.eye_cascade = None
        self.profile_cascade = None

        # 初始化OpenCV人脸检测器
        self._init_opencv_detectors()

    def _get_subprocess_kwargs(self):
        """获取subprocess调用的通用参数，在Windows下隐藏命令窗口"""
        kwargs = {
            'capture_output': True,
            'text': True
        }

        # 在Windows下隐藏命令窗口
        if sys.platform == 'win32':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

        return kwargs

    def _init_opencv_detectors(self):
        """初始化OpenCV检测器"""
        try:
            # 加载Haar级联分类器
            cascade_path = cv2.data.haarcascades
            
            # 正面人脸检测器
            face_cascade_path = os.path.join(cascade_path, 'haarcascade_frontalface_default.xml')
            if os.path.exists(face_cascade_path):
                self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
            
            # 眼睛检测器（用于提高准确性）
            eye_cascade_path = os.path.join(cascade_path, 'haarcascade_eye.xml')
            if os.path.exists(eye_cascade_path):
                self.eye_cascade = cv2.CascadeClassifier(eye_cascade_path)
            
            # 侧面人脸检测器
            profile_cascade_path = os.path.join(cascade_path, 'haarcascade_profileface.xml')
            if os.path.exists(profile_cascade_path):
                self.profile_cascade = cv2.CascadeClassifier(profile_cascade_path)
            
            if self.face_cascade is None:
                print("警告: 无法加载OpenCV人脸检测器，将使用备选方法")
                
        except Exception as e:
            print(f"初始化OpenCV检测器失败: {e}")
    
    def detect_faces_in_image(self, image_path: str, confidence_threshold: float = 0.5) -> bool:
        """
        检测图像中是否包含人脸
        
        Args:
            image_path: 图像文件路径
            confidence_threshold: 置信度阈值
            
        Returns:
            是否检测到人脸
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图像: {image_path}")
                return False
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 使用多种方法检测人脸
            faces_detected = False
            
            # 方法1: 使用正面人脸检测器
            if self.face_cascade is not None:
                faces = self.face_cascade.detectMultiScale(
                    gray,
                    scaleFactor=1.1,
                    minNeighbors=5,
                    minSize=(30, 30),
                    flags=cv2.CASCADE_SCALE_IMAGE
                )
                
                if len(faces) > 0:
                    # 如果有眼睛检测器，进一步验证
                    if self.eye_cascade is not None:
                        for (x, y, w, h) in faces:
                            roi_gray = gray[y:y+h, x:x+w]
                            eyes = self.eye_cascade.detectMultiScale(roi_gray)
                            if len(eyes) >= 1:  # 至少检测到一只眼睛
                                faces_detected = True
                                break
                    else:
                        faces_detected = True
            
            # 方法2: 如果正面检测失败，尝试侧面检测
            if not faces_detected and self.profile_cascade is not None:
                profile_faces = self.profile_cascade.detectMultiScale(
                    gray,
                    scaleFactor=1.1,
                    minNeighbors=5,
                    minSize=(30, 30)
                )
                if len(profile_faces) > 0:
                    faces_detected = True
            
            # 方法3: 使用DNN模型（如果可用）
            if not faces_detected:
                faces_detected = self._detect_faces_with_dnn(image)
            
            return faces_detected
            
        except Exception as e:
            print(f"人脸检测失败: {e}")
            return False
    
    def _detect_faces_with_dnn(self, image: np.ndarray) -> bool:
        """
        使用DNN模型检测人脸（备选方法）
        
        Args:
            image: 输入图像
            
        Returns:
            是否检测到人脸
        """
        try:
            # 尝试使用OpenCV的DNN人脸检测
            # 这需要预训练的模型文件，如果没有则跳过
            
            # 简单的肤色检测作为备选
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 定义肤色范围
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            # 创建肤色掩码
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (11, 11))
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(skin_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 检查是否有足够大的肤色区域
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 1000:  # 最小面积阈值
                    return True
            
            return False
            
        except Exception:
            return False
    
    def extract_video_frames(self, video_path: str, output_dir: str, 
                           fps: float = 1.0, duration: Optional[float] = None) -> List[str]:
        """
        从视频中提取帧
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            fps: 提取帧率
            duration: 视频时长（如果提供，将在中间位置提取一帧）
            
        Returns:
            提取的图像文件路径列表
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            if duration is not None:
                # 在视频中间位置提取一帧
                middle_time = duration / 2
                output_path = os.path.join(output_dir, "frame_middle.jpg")
                
                cmd = [
                    self.ffmpeg_path,
                    "-i", video_path,
                    "-ss", str(middle_time),
                    "-vframes", "1",
                    "-y",
                    output_path
                ]
                
                kwargs = self._get_subprocess_kwargs()
                kwargs['timeout'] = 30
                result = subprocess.run(cmd, **kwargs)
                if result.returncode == 0 and os.path.exists(output_path):
                    return [output_path]
                else:
                    print(f"提取视频帧失败: {result.stderr}")
                    return []
            else:
                # 按指定帧率提取多帧
                output_pattern = os.path.join(output_dir, "frame_%04d.jpg")
                
                cmd = [
                    self.ffmpeg_path,
                    "-i", video_path,
                    "-vf", f"fps={fps}",
                    "-y",
                    output_pattern
                ]
                
                kwargs = self._get_subprocess_kwargs()
                kwargs['timeout'] = 60
                result = subprocess.run(cmd, **kwargs)
                if result.returncode == 0:
                    # 获取生成的文件列表
                    frame_files = []
                    for file in os.listdir(output_dir):
                        if file.startswith("frame_") and file.endswith(".jpg"):
                            frame_files.append(os.path.join(output_dir, file))
                    return sorted(frame_files)
                else:
                    print(f"提取视频帧失败: {result.stderr}")
                    return []
                    
        except Exception as e:
            print(f"提取视频帧失败: {e}")
            return []
    
    def check_video_has_faces(self, video_path: str, temp_dir: Optional[str] = None) -> bool:
        """
        检查视频是否包含人脸
        
        Args:
            video_path: 视频文件路径
            temp_dir: 临时目录（如果不提供将自动创建）
            
        Returns:
            是否检测到人脸
        """
        try:
            # 获取视频信息
            video_info = self._get_video_duration(video_path)
            if not video_info:
                return False
            
            duration = video_info.get('duration', 0)
            if duration <= 0:
                return False
            
            # 创建临时目录
            if temp_dir is None:
                temp_dir = tempfile.mkdtemp()
                cleanup_temp = True
            else:
                cleanup_temp = False
            
            try:
                # 提取视频帧（在中间位置提取一帧）
                frame_files = self.extract_video_frames(video_path, temp_dir, duration=duration)
                
                if not frame_files:
                    return False
                
                # 检测人脸
                has_faces = False
                for frame_file in frame_files:
                    if self.detect_faces_in_image(frame_file):
                        has_faces = True
                        break
                
                return has_faces
                
            finally:
                # 清理临时文件
                if cleanup_temp:
                    try:
                        import shutil
                        shutil.rmtree(temp_dir)
                    except:
                        pass
                else:
                    # 只清理提取的帧文件
                    for frame_file in frame_files:
                        try:
                            os.remove(frame_file)
                        except:
                            pass
                            
        except Exception as e:
            print(f"检查视频人脸失败: {e}")
            return False
    
    def _get_video_duration(self, video_path: str) -> Optional[dict]:
        """获取视频时长"""
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                video_path
            ]
            
            kwargs = self._get_subprocess_kwargs()
            kwargs['timeout'] = 30
            result = subprocess.run(cmd, **kwargs)
            if result.returncode == 0:
                import json
                info = json.loads(result.stdout)
                duration = float(info.get('format', {}).get('duration', 0))
                return {'duration': duration}
            
            return None
            
        except Exception:
            return None
    
    def process_video_list(self, video_paths: List[str], temp_dir: str, 
                          progress_callback=None) -> List[str]:
        """
        批量处理视频列表，删除包含人脸的视频
        
        Args:
            video_paths: 视频文件路径列表
            temp_dir: 临时目录
            progress_callback: 进度回调函数
            
        Returns:
            被删除的视频文件路径列表
        """
        deleted_videos = []
        total_videos = len(video_paths)
        
        for i, video_path in enumerate(video_paths):
            try:
                if progress_callback:
                    progress = (i / total_videos) * 100
                    progress_callback(progress, f"检查视频 {i+1}/{total_videos}: {Path(video_path).name}")
                
                print(f"正在检查视频是否包含人脸: {video_path}")
                
                if self.check_video_has_faces(video_path, temp_dir):
                    print(f"检测到人脸，删除视频: {video_path}")
                    try:
                        os.remove(video_path)
                        deleted_videos.append(video_path)
                    except Exception as e:
                        print(f"删除视频失败: {e}")
                else:
                    print(f"未检测到人脸: {video_path}")
                    
            except Exception as e:
                print(f"处理视频失败 {video_path}: {e}")
        
        if progress_callback:
            progress_callback(100, f"人脸检测完成，删除了 {len(deleted_videos)} 个视频")
        
        return deleted_videos


# 使用示例
if __name__ == "__main__":
    detector = FaceDetector()
    
    # 测试图像人脸检测
    test_image = "test_image.jpg"
    if os.path.exists(test_image):
        has_face = detector.detect_faces_in_image(test_image)
        print(f"图像 {test_image} 是否包含人脸: {has_face}")
    
    # 测试视频人脸检测
    test_video = "test_video.mp4"
    if os.path.exists(test_video):
        has_face = detector.check_video_has_faces(test_video)
        print(f"视频 {test_video} 是否包含人脸: {has_face}")
    
    # 测试批量处理
    video_list = ["video1.mp4", "video2.mp4"]  # 示例视频列表
    existing_videos = [v for v in video_list if os.path.exists(v)]
    
    if existing_videos:
        def progress_callback(progress, message):
            print(f"进度: {progress:.1f}% - {message}")
        
        deleted = detector.process_video_list(existing_videos, "temp", progress_callback)
        print(f"删除的视频: {deleted}")
