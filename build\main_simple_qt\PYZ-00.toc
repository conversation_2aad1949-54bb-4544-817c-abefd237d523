('C:\\Users\\<USER>\\Desktop\\已逆向\\当前发布稳定版本\\video_qiege\\build\\main_simple_qt\\PYZ-00.pyz',
 [('OpenSSL',
   'D:\\Python\\Python39-64\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\Python\\Python39-64\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\Python\\Python39-64\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\Python\\Python39-64\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\Python\\Python39-64\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Python\\Python39-64\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\Python\\Python39-64\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Python\\Python39-64\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Python\\Python39-64\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Python\\Python39-64\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\Python\\Python39-64\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Python39-64\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Python39-64\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Python\\Python39-64\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Python\\Python39-64\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Python\\Python39-64\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Python\\Python39-64\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Python39-64\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\Python\\Python39-64\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Python\\Python39-64\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Python\\Python39-64\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Python39-64\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Python\\Python39-64\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Python\\Python39-64\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Python\\Python39-64\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Python\\Python39-64\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Python\\Python39-64\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Python\\Python39-64\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Python\\Python39-64\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Python\\Python39-64\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\Python\\Python39-64\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Python\\Python39-64\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Python\\Python39-64\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Python\\Python39-64\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Python\\Python39-64\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Python\\Python39-64\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Python\\Python39-64\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Python\\Python39-64\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Python\\Python39-64\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Python\\Python39-64\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Python\\Python39-64\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Python\\Python39-64\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Python\\Python39-64\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Python\\Python39-64\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Python\\Python39-64\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Python\\Python39-64\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Python\\Python39-64\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('auth', '-', 'PYMODULE'),
  ('auth.eydata_auth',
   'C:\\Users\\<USER>\\Desktop\\已逆向\\当前发布稳定版本\\video_qiege\\auth\\eydata_auth.py',
   'PYMODULE'),
  ('base64', 'D:\\Python\\Python39-64\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Python\\Python39-64\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Python39-64\\lib\\bisect.py', 'PYMODULE'),
  ('brotli',
   'D:\\Python\\Python39-64\\lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bz2', 'D:\\Python\\Python39-64\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Python39-64\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\Python\\Python39-64\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Python\\Python39-64\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('chardet',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\Python\\Python39-64\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Python\\Python39-64\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\Python\\Python39-64\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Python\\Python39-64\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Python\\Python39-64\\lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Python\\Python39-64\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Python\\Python39-64\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Python\\Python39-64\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Python\\Python39-64\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Python\\Python39-64\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\Python\\Python39-64\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\Python39-64\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Python39-64\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Python39-64\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\Python\\Python39-64\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\Python\\Python39-64\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python\\Python39-64\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\Python\\Python39-64\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\Python\\Python39-64\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Python\\Python39-64\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Python\\Python39-64\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Python\\Python39-64\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Python\\Python39-64\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\Python\\Python39-64\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Python\\Python39-64\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Python39-64\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Python\\Python39-64\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Python\\Python39-64\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Python\\Python39-64\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Python39-64\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\Python\\Python39-64\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\Python\\Python39-64\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Python39-64\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Python39-64\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Python\\Python39-64\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Python\\Python39-64\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Python\\Python39-64\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Python\\Python39-64\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Python39-64\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Python\\Python39-64\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Python\\Python39-64\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Python\\Python39-64\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Python\\Python39-64\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Python\\Python39-64\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Python39-64\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Python\\Python39-64\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Python\\Python39-64\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Python\\Python39-64\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Python\\Python39-64\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Python\\Python39-64\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\Python\\Python39-64\\lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'D:\\Python\\Python39-64\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Python39-64\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Python39-64\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Python\\Python39-64\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Python39-64\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Python\\Python39-64\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Python39-64\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Python39-64\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Python39-64\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\Python39-64\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Python\\Python39-64\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Python\\Python39-64\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Python\\Python39-64\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Python\\Python39-64\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\Python\\Python39-64\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Python\\Python39-64\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Python\\Python39-64\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server', 'D:\\Python\\Python39-64\\lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'D:\\Python\\Python39-64\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Python\\Python39-64\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Python\\Python39-64\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Python\\Python39-64\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Python\\Python39-64\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Python\\Python39-64\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Python\\Python39-64\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Python39-64\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Python39-64\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\Python\\Python39-64\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Python\\Python39-64\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Python39-64\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Python39-64\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Python39-64\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Python\\Python39-64\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Python\\Python39-64\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Python39-64\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Python\\Python39-64\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Python\\Python39-64\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Python\\Python39-64\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Python\\Python39-64\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging', 'D:\\Python\\Python39-64\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers',
   'D:\\Python\\Python39-64\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'D:\\Python\\Python39-64\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Python\\Python39-64\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Python\\Python39-64\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Python\\Python39-64\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Python\\Python39-64\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Python39-64\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Python\\Python39-64\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Python\\Python39-64\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\Python\\Python39-64\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python\\Python39-64\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Python\\Python39-64\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Python39-64\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Python\\Python39-64\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Python\\Python39-64\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Python39-64\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\Python\\Python39-64\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Python\\Python39-64\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Python\\Python39-64\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Python\\Python39-64\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\Python\\Python39-64\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Python\\Python39-64\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Python\\Python39-64\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\Python\\Python39-64\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'D:\\Python\\Python39-64\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\Python39-64\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Python39-64\\lib\\random.py', 'PYMODULE'),
  ('readline',
   'D:\\Python\\Python39-64\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('requests',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Python\\Python39-64\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'D:\\Python\\Python39-64\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Python\\Python39-64\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Python39-64\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\Python\\Python39-64\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Python39-64\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\Python39-64\\lib\\signal.py', 'PYMODULE'),
  ('smtplib', 'D:\\Python\\Python39-64\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\Python\\Python39-64\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\Python\\Python39-64\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'D:\\Python\\Python39-64\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Python39-64\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Python\\Python39-64\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Python39-64\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python\\Python39-64\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Python\\Python39-64\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Python39-64\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python\\Python39-64\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Python39-64\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Python39-64\\lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('token', 'D:\\Python\\Python39-64\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python\\Python39-64\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Python39-64\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\Python\\Python39-64\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Python\\Python39-64\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Python\\Python39-64\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Python\\Python39-64\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Python\\Python39-64\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Python\\Python39-64\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Python\\Python39-64\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Python\\Python39-64\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Python\\Python39-64\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Python\\Python39-64\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Python\\Python39-64\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Python\\Python39-64\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Python\\Python39-64\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'D:\\Python\\Python39-64\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\Python\\Python39-64\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Python\\Python39-64\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Python\\Python39-64\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Python\\Python39-64\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Python\\Python39-64\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('utils', '-', 'PYMODULE'),
  ('utils.device_id',
   'C:\\Users\\<USER>\\Desktop\\已逆向\\当前发布稳定版本\\video_qiege\\utils\\device_id.py',
   'PYMODULE'),
  ('uu', 'D:\\Python\\Python39-64\\lib\\uu.py', 'PYMODULE'),
  ('video', '-', 'PYMODULE'),
  ('video.face_detector',
   'C:\\Users\\<USER>\\Desktop\\已逆向\\当前发布稳定版本\\video_qiege\\video\\face_detector.py',
   'PYMODULE'),
  ('video.video_processor',
   'C:\\Users\\<USER>\\Desktop\\已逆向\\当前发布稳定版本\\video_qiege\\video\\video_processor.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\Python\\Python39-64\\lib\\webbrowser.py', 'PYMODULE'),
  ('win32con',
   'D:\\Python\\Python39-64\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\Python\\Python39-64\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\Python\\Python39-64\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\Python\\Python39-64\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\Python\\Python39-64\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Python\\Python39-64\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Python\\Python39-64\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Python\\Python39-64\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Python\\Python39-64\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Python\\Python39-64\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Python\\Python39-64\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Python\\Python39-64\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Python\\Python39-64\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Python\\Python39-64\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Python\\Python39-64\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Python\\Python39-64\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Python\\Python39-64\\lib\\zipimport.py', 'PYMODULE')])
