# 闪灵猫-分割 Python版 依赖包

# 核心依赖
requests>=2.28.0
psutil>=5.9.0
numpy>=1.21.0

# GUI框架
PyQt5>=5.15.0

# 图像和视频处理
opencv-python>=4.6.0
Pillow>=9.0.0

# 二维码生成 (已移除)
# qrcode[pil]>=7.3.0

# GUI相关 (tkinter是Python内置的，但某些Linux发行版需要单独安装)
# 在Ubuntu/Debian上: sudo apt-get install python3-tk
# 在CentOS/RHEL上: sudo yum install tkinter 或 sudo dnf install python3-tkinter

# 可选依赖（用于更好的人脸检测）
# dlib>=19.22.0  # 需要编译，安装较复杂
# face-recognition>=1.3.0  # 基于dlib的高级人脸识别库

# 开发和测试依赖
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0
