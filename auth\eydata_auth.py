"""
易游网络验证模块
基于python_eydata_demo.py改进的验证系统
"""
import requests
from urllib.parse import urlencode
from typing import Optional, Dict, Any
from utils.device_id import DeviceIDGenerator

class EydataAuth:
    """易游网络验证类"""
    
    def __init__(self, single_code: str, version: str = "1.0"):
        self.http_url = "http://w.eydata.net"
        self.single_code = single_code
        self.version = version
        generator = DeviceIDGenerator()
        
        self.device_id = generator.generate_device_id()
        self.is_logged_in = False
        self.login_token = None
        self.expire_time = None
        
    def http_post(self, url: str, data: Dict[str, Any]) -> str:
        """发送HTTP POST请求"""
        try:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            if not url.startswith('/'):
                url = '/' + url
            url_encoded = urlencode([(key, value) for key, value in data.items()])
            r = requests.post(
                self.http_url + url, 
                data=url_encoded, 
                headers=headers, 
                timeout=30
            )
            if r.status_code == 200:
                result = str(r.text)
            else:
                result = "遇到错误，请检查网络重试"
            return result
        except Exception as e:
            print(f"网络请求错误: {e}")
            return "遇到错误，请检查网络重试"
    
    def login(self) -> tuple[bool, str]:
        """登录验证"""
        try:
            # 使用已经在初始化时生成的设备ID
            device_id = self.device_id
            data = {
                'SingleCode': self.single_code,
                'Ver': self.version,
                'Mac': device_id,
            }
            
            result = self.http_post('/F22DAD5C780E9562', data)
            print(f"登录响应: {result}")
            
            if len(result) == 32:
                self.is_logged_in = True
                self.login_token = result
                print(f'登录成功，状态码：{result}')
                return True, f'登录成功，状态码：{result}'
            else:
                self.is_logged_in = False
                print(f'登录失败，错误码：{result}')
                return False, f'登录失败，错误码：{result}'
                
        except Exception as e:
            error_msg = f"登录过程中发生错误: {str(e)}"
            print(error_msg)
            return False, error_msg
    
    def check_status(self) -> tuple[bool, str]:
        """检查登录状态（心跳检测）"""
        if not self.is_logged_in or not self.login_token:
            return False, "未登录"
        
        try:
            # 这里可以添加心跳检测的API调用
            # 目前简单返回登录状态
            return True, "状态正常"
        except Exception as e:
            return False, f"状态检查失败: {str(e)}"
    
    def logout(self):
        """登出"""
        self.is_logged_in = False
        self.login_token = None
        self.expire_time = None

