import mysql.connector
from mysql.connector import Error
import re
import os
from datetime import datetime
from typing import Optional, List, Dict

class DatabaseManager:
    """数据库管理器 - 处理视频分割记录"""
    
    def __init__(self, host='*************', user='root', password='root', database='video_mixed'):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        
    def connect(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                autocommit=True
            )
            
            if self.connection.is_connected():
                print(f"成功连接到MySQL数据库: {self.database}")
                self.create_table_if_not_exists()
                return True
                
        except Error as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def create_table_if_not_exists(self):
        """创建视频分割记录表"""
        try:
            cursor = self.connection.cursor()
            
            create_table_query = """
            CREATE TABLE IF NOT EXISTS video_split_records (
                video_id VARCHAR(50) PRIMARY KEY COMMENT '视频ID',
                video_filename VARCHAR(500) NOT NULL COMMENT '视频文件名',
                split_time DATETIME NOT NULL COMMENT '分割时间',
                segments_count INT DEFAULT 0 COMMENT '分割片段数量',
                status ENUM('processing', 'completed', 'failed') DEFAULT 'processing' COMMENT '处理状态',
                file_path VARCHAR(1000) COMMENT '视频文件路径',
                output_dir VARCHAR(1000) COMMENT '输出目录',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频分割记录表';
            """
            
            cursor.execute(create_table_query)
            print("视频分割记录表创建/检查完成")
            cursor.close()
            
        except Error as e:
            print(f"创建表失败: {e}")
    
    def extract_video_id(self, filename: str) -> Optional[str]:
        """从文件名中提取视频ID"""
        try:
            # 提取文件名（不含路径）
            basename = os.path.basename(filename)
            
            # 使用正则表达式提取开头的数字ID（至少10位数字）
            match = re.match(r'^(\d{10,})_', basename)
            if match:
                video_id = match.group(1)
                print(f"提取到视频ID: {video_id} from {basename}")
                return video_id
            
            print(f"无法从文件名提取视频ID: {basename} (不符合格式)")
            return None
            
        except Exception as e:
            print(f"提取视频ID失败: {e}")
            return None
    
    def is_video_processed(self, video_id: str) -> bool:
        """检查视频是否已经处理过"""
        try:
            cursor = self.connection.cursor()
            
            query = "SELECT video_id FROM video_split_records WHERE video_id = %s"
            cursor.execute(query, (video_id,))
            
            result = cursor.fetchone()
            cursor.close()
            
            return result is not None
            
        except Error as e:
            print(f"查询视频处理状态失败: {e}")
            return False
    
    def record_video_processing(self, video_id: str, filename: str, file_path: str, output_dir: str) -> bool:
        """记录视频开始处理"""
        try:
            cursor = self.connection.cursor()
            
            insert_query = """
            INSERT INTO video_split_records 
            (video_id, video_filename, split_time, status, file_path, output_dir)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            values = (
                video_id,
                filename,
                datetime.now(),
                'processing',
                file_path,
                output_dir
            )
            
            cursor.execute(insert_query, values)
            cursor.close()
            
            print(f"记录视频处理开始: {video_id}")
            return True
            
        except Error as e:
            print(f"记录视频处理失败: {e}")
            return False
    
    def update_video_completion(self, video_id: str, segments_count: int, status: str = 'completed') -> bool:
        """更新视频处理完成状态"""
        try:
            cursor = self.connection.cursor()
            
            update_query = """
            UPDATE video_split_records 
            SET segments_count = %s, status = %s, updated_at = %s
            WHERE video_id = %s
            """
            
            values = (segments_count, status, datetime.now(), video_id)
            cursor.execute(update_query, values)
            cursor.close()
            
            print(f"更新视频处理状态: {video_id} -> {status}")
            return True
            
        except Error as e:
            print(f"更新视频状态失败: {e}")
            return False
    
    def get_processed_videos(self) -> List[Dict]:
        """获取已处理的视频列表"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            query = """
            SELECT video_id, video_filename, split_time, segments_count, status
            FROM video_split_records 
            ORDER BY split_time DESC
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            
            return results
            
        except Error as e:
            print(f"获取已处理视频列表失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("数据库连接已关闭")