#!/usr/bin/env python3
"""
闪灵猫-分割 Python版 (PyQt5界面)
智能视频场景分割工具

主要功能：
1. 智能场景检测和分割
2. 人脸检测过滤
3. 视频尺寸调整和裁剪
4. 批量处理多个视频
5. 易游网络授权验证

版本: 2.0.4
"""
import sys
import os
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QGridLayout, QLabel, QPushButton,
                             QLineEdit, QTextEdit, QComboBox, QSpinBox,
                             QDoubleSpinBox, QCheckBox, QProgressBar, QGroupBox,
                             QFileDialog, QMessageBox, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QThread
from PyQt5.QtGui import QFont, QPixmap

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auth.eydata_auth import EydataAuth
from utils.device_id import get_device_id
from video.video_processor import VideoProcessor
from video.face_detector import FaceDetector
from database.db_manager import DatabaseManager


class VideoProcessingThread(QThread):
    """视频处理线程"""

    progress_updated = pyqtSignal(int, str)
    processing_finished = pyqtSignal(bool, str)

    def __init__(self, video_files, output_dir, settings, db_manager=None):
        super().__init__()
        self.video_files = video_files
        self.output_dir = output_dir
        self.settings = settings
        self.processor = None
        self.is_cancelled = False
        self.db_manager = db_manager

    def run(self):
        """运行视频处理"""
        try:
            # 创建视频处理器
            self.processor = VideoProcessor()
            self.processor.set_progress_callback(self.on_progress)

            total_files = len(self.video_files)
            processed_count = 0
            skipped_count = 0

            for i, video_file in enumerate(self.video_files):
                if self.is_cancelled:
                    break

                filename = os.path.basename(video_file)
                self.progress_updated.emit(
                    int((i / total_files) * 100),
                    f"正在处理: {filename} ({i+1}/{total_files})"
                )

                try:
                    video_id = None
                    
                    # 提取视频ID（无论是否启用数据库检查都要提取，用于判断是否记录）
                    if self.db_manager and self.db_manager.connection:
                        video_id = self.db_manager.extract_video_id(filename)
                    
                    # 🔧 修复：只有在启用数据库检查时才进行重复检查
                    enable_db_check = self.settings.get('enable_db_check', True)
                    
                    if (enable_db_check and self.db_manager and 
                        self.db_manager.connection and video_id):
                        
                        if self.db_manager.is_video_processed(video_id):
                            skipped_count += 1
                            self.progress_updated.emit(
                                int(((i + 1) / total_files) * 100),
                                f"⏭️ 跳过: {filename} - 已处理过 (ID: {video_id})"
                            )
                            continue

                    # 第一次处理时创建带时间戳的素材库文件夹
                    if i == 0:
                        import datetime
                        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                        self.main_output_dir = os.path.join(self.output_dir, f"素材库-{timestamp}")
                        os.makedirs(self.main_output_dir, exist_ok=True)

                    # 直接使用主输出目录，不创建子文件夹
                    video_output_dir = self.main_output_dir

                    # 记录开始处理到数据库（仅在有video_id时记录）
                    if (self.db_manager and self.db_manager.connection and video_id):
                        self.db_manager.record_video_processing(
                            video_id, filename, video_file, video_output_dir
                        )
                        self.progress_updated.emit(
                            int((i / total_files) * 100),
                            f"📊 记录到数据库: {filename} (ID: {video_id})"
                        )
                    elif not video_id:
                        self.progress_updated.emit(
                            int((i / total_files) * 100),
                            f"ℹ️ 无视频ID，跳过数据库记录: {filename}"
                        )

                    # 获取视频信息
                    video_info = self.processor.get_video_info(video_file)
                    duration = video_info['duration']
                    width = video_info['width']
                    height = video_info['height']

                    # 检查视频宽高比
                    if width / height > 3 / 4:
                        self.progress_updated.emit(
                            int(((i + 1) / total_files) * 100),
                            f"⚠️ 跳过: {filename} - 宽高比大于3:4，无法裁剪"
                        )
                        
                        # 更新数据库状态为失败（仅在有video_id时）
                        if self.db_manager and self.db_manager.connection and video_id:
                            self.db_manager.update_video_completion(video_id, 0, 'failed')
                        continue

                    # 检测场景变化
                    time_points = self.processor.detect_scene_changes(
                        video_file,
                        threshold=self.settings.get('threshold', 1.0)
                    )

                    if not time_points or len(time_points) < 2:
                        self.progress_updated.emit(
                            int(((i + 1) / total_files) * 100),
                            f"⚠️ 跳过: {filename} - 未能识别出分镜"
                        )
                        
                        # 更新数据库状态为失败（仅在有video_id时）
                        if self.db_manager and self.db_manager.connection and video_id:
                            self.db_manager.update_video_completion(video_id, 0, 'failed')
                        continue

                    # 添加视频结束时间点
                    if time_points[-1] != duration:
                        time_points.append(duration)

                    # 执行视频分割
                    video_basename = os.path.splitext(filename)[0]
                    output_prefix = f"{video_basename}"

                    split_settings = {
                        'threshold': self.settings.get('threshold', 1),
                        'min_segment_duration': self.settings.get('min_duration', 1),
                        'min_save_segment_duration': self.settings.get('min_save_duration', 3),
                        'max_save_count': self.settings.get('max_count', 200),
                        'crop_scale': self.settings.get('crop_scale', 1.0),
                        'video_width': 1080,
                        'video_height': 1440 if "1440" in self.settings.get('video_size', '') else 1920,
                        'is_top': self.settings.get('is_top', True),
                        'is_merge': self.settings.get('is_merge', True),
                        'delete_face_video': self.settings.get('face_detection', False)
                    }

                    split_result = self.processor.split_video_original_logic(
                        time_points,
                        video_file,
                        width,
                        height,
                        video_output_dir,
                        output_prefix,
                        split_settings
                    )

                    processed_count += 1
                    successful_count = split_result.get('successful', 0)

                    # 更新数据库完成状态（仅在有video_id时）
                    if self.db_manager and self.db_manager.connection and video_id:
                        self.db_manager.update_video_completion(video_id, successful_count, 'completed')

                    self.progress_updated.emit(
                        int(((i + 1) / total_files) * 100),
                        f"✅ 完成: {filename} - 成功生成 {successful_count} 个片段"
                    )

                except Exception as e:
                    # 处理失败时更新数据库状态（仅在有video_id时）
                    if self.db_manager and self.db_manager.connection and video_id:
                        self.db_manager.update_video_completion(video_id, 0, 'failed')
                    
                    self.progress_updated.emit(
                        int(((i + 1) / total_files) * 100),
                        f"❌ 处理失败: {filename} - {str(e)}"
                    )

            if self.is_cancelled:
                self.processing_finished.emit(False, "处理已被用户取消")
            else:
                message = f"🎉 处理完成！成功处理了 {processed_count}/{total_files} 个视频文件"
                if skipped_count > 0:
                    message += f"，跳过 {skipped_count} 个已处理的视频"
                self.processing_finished.emit(True, message)

        except Exception as e:
            self.processing_finished.emit(False, f"处理过程中发生错误: {str(e)}")

    def on_progress(self, progress, message):
        """进度回调"""
        # 这里可以处理FFmpeg的详细进度
        pass

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        if self.processor:
            self.processor.cancel_processing()


class AuthDialog(QWidget):
    """授权验证对话框"""
    
    auth_success = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.auth = None  # 将在用户输入卡密后初始化
        self.device_id = get_device_id()
        self.config_file = os.path.join(os.path.expanduser("~"), ".yuye_auth.json")
        self.init_ui()
        self.load_saved_card_key()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("授权验证 - 视频分割工具")
        self.setFixedSize(500, 480)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                font-family: "Microsoft YaHei";
            }
            QLabel {
                color: #333333;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                font-size: 12px;
            }
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton#authButton {
                background-color: #4A90E2;
                color: white;
            }
            QPushButton#authButton:hover {
                background-color: #357ABD;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题
        title_label = QLabel("裕页-分割")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #333;")
        layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("智能视频场景分割工具 - 授权验证")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("font-size: 16px; color: #666;")
        layout.addWidget(subtitle_label)
        
        # 设备ID区域
        device_label = QLabel("设备ID:")
        device_label.setStyleSheet("font-size: 14px; color: #333;")
        layout.addWidget(device_label)
        
        device_layout = QHBoxLayout()
        self.device_input = QLineEdit(self.device_id)
        self.device_input.setReadOnly(True)
        self.device_input.setStyleSheet("background-color: #f8f8f8;")
        device_layout.addWidget(self.device_input)
        
        copy_btn = QPushButton("复制")
        copy_btn.clicked.connect(self.copy_device_id)
        device_layout.addWidget(copy_btn)
        layout.addLayout(device_layout)

        # 添加间距
        layout.addSpacing(20)

        # 卡密输入区域
        card_label = QLabel("卡密:")
        card_label.setStyleSheet("font-size: 14px; color: #333;")
        layout.addWidget(card_label)

        self.card_input = QLineEdit()
        self.card_input.setPlaceholderText("请输入您的卡密")
        self.card_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
            }
        """)
        layout.addWidget(self.card_input)

        # 添加间距
        layout.addSpacing(20)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 验证按钮
        self.auth_button = QPushButton("验证授权")
        self.auth_button.setObjectName("authButton")
        self.auth_button.clicked.connect(self.verify_auth)
        button_layout.addWidget(self.auth_button)

        # 清除记住的卡密按钮
        self.clear_button = QPushButton("清除卡密")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
        """)
        self.clear_button.clicked.connect(self.clear_saved_card_key)
        button_layout.addWidget(self.clear_button)

        layout.addLayout(button_layout)
        
        # 状态信息
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 12px; color: #666;")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        self.center_on_screen()
    
    def center_on_screen(self):
        """窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def copy_device_id(self):
        """复制设备ID"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.device_id)
        QMessageBox.information(self, "提示", "设备ID已复制到剪贴板")

    def save_card_key(self, card_key):
        """保存卡密到配置文件"""
        try:
            import json
            import base64

            # 简单编码卡密
            encoded_key = base64.b64encode(card_key.encode()).decode()

            config = {
                "card_key": encoded_key,
                "device_id": self.device_id,
                "save_time": str(QTimer().remainingTime())
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)

        except Exception as e:
            print(f"保存卡密失败: {e}")

    def load_saved_card_key(self):
        """加载保存的卡密"""
        try:
            import json
            import base64

            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 解码卡密
                encoded_key = config.get("card_key", "")
                if encoded_key:
                    card_key = base64.b64decode(encoded_key.encode()).decode()
                    self.card_input.setText(card_key)

                    # 自动尝试登录
                    QTimer.singleShot(1000, self.auto_login)

        except Exception as e:
            print(f"加载卡密失败: {e}")

    def auto_login(self):
        """自动登录"""
        card_key = self.card_input.text().strip()
        if card_key:
            self.status_label.setText("正在自动登录...")
            self.status_label.setStyleSheet("color: #4A90E2; font-size: 12px;")
            QTimer.singleShot(500, self.verify_auth)

    def clear_saved_card_key(self):
        """清除保存的卡密"""
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)

            self.card_input.clear()
            self.status_label.setText("已清除记住的卡密")
            self.status_label.setStyleSheet("color: #4A90E2; font-size: 12px;")

        except Exception as e:
            print(f"清除卡密失败: {e}")
            self.status_label.setText("清除失败")
            self.status_label.setStyleSheet("color: #F44336; font-size: 12px;")
    
    def verify_auth(self):
        """验证授权"""
        # 检查卡密输入
        card_key = self.card_input.text().strip()
        if not card_key:
            self.status_label.setText("请输入卡密")
            self.status_label.setStyleSheet("color: #F44336; font-size: 12px;")
            return

        # 使用用户输入的卡密初始化认证对象
        from auth.eydata_auth import EydataAuth
        self.auth = EydataAuth(card_key)

        self.auth_button.setEnabled(False)
        self.auth_button.setText("验证中...")
        self.status_label.setText("正在验证授权...")

        try:
            success, message = self.auth.login()
            if success:
                # 保存卡密
                card_key = self.card_input.text().strip()
                self.save_card_key(card_key)

                self.status_label.setText("授权验证成功!")
                self.status_label.setStyleSheet("color: #4CAF50; font-size: 12px;")
                QTimer.singleShot(1000, self.accept_auth)
            else:
                self.status_label.setText(f"验证失败: {message}")
                self.status_label.setStyleSheet("color: #F44336; font-size: 12px;")
                self.auth_button.setEnabled(True)
                self.auth_button.setText("验证授权")
        except Exception as e:
            self.status_label.setText(f"验证失败: {str(e)}")
            self.status_label.setStyleSheet("color: #F44336; font-size: 12px;")
            self.auth_button.setEnabled(True)
            self.auth_button.setText("验证授权")
    
    def accept_auth(self):
        """接受授权"""
        self.auth_success.emit()
        self.close()


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.video_files = []
        self.output_dir = ""
        self.is_authenticated = False
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        self.db_connected = False   
        
        self.init_ui()
        self.show_auth_dialog()
        
        # 尝试连接数据库
        self.connect_database()
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.db_connected = self.db_manager.connect()
            if self.db_connected:
                self.log("✅ 数据库连接成功")
            else:
                self.log("❌ 数据库连接失败，将跳过重复检查功能")
        except Exception as e:
            self.log(f"❌ 数据库连接异常: {str(e)}")
            self.db_connected = False
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("裕页-分割 v1.0.0 - 智能视频场景分割工具")
        self.setGeometry(100, 100, 1000, 750)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
            }
            QComboBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
                color: #333;
                min-width: 120px;
            }
            QComboBox:hover {
                border: 2px solid #4A90E2;
                background-color: #f0f8ff;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #ddd;
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
                background-color: #f0f0f0;
            }
            QComboBox::drop-down:hover {
                background-color: #e0e0e0;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #666;
                width: 0px;
                height: 0px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                background-color: white;
                color: #333;
                selection-background-color: #4A90E2;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px;
                border: none;
                background-color: white;
                color: #333;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
                color: #333;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #4A90E2;
                color: white;
            }
        """)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setCentralWidget(scroll_area)

        # 创建中央部件
        central_widget = QWidget()
        scroll_area.setWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建各个区域
        self.create_file_section(main_layout)
        self.create_settings_section(main_layout)
        self.create_action_section(main_layout)
        self.create_log_section(main_layout)
    
    def create_file_section(self, parent_layout):
        """创建文件处理区域"""
        file_group = QGroupBox("视频文件选择")
        file_layout = QGridLayout()

        # 视频选择
        self.select_video_btn = QPushButton("选择要分割的视频")
        self.select_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
        """)
        self.select_video_btn.clicked.connect(self.select_videos)
        file_layout.addWidget(self.select_video_btn, 0, 0)

        self.video_status = QLabel("请选择需要分割的视频文件")
        self.video_status.setStyleSheet("color: #666; font-size: 13px; padding: 10px;")
        # 设置文本换行和最大宽度，防止长文件名撑开界面
        self.video_status.setWordWrap(True)
        self.video_status.setMaximumWidth(400)
        file_layout.addWidget(self.video_status, 0, 1, 1, 2)

        # 输出目录选择
        self.select_output_btn = QPushButton("选择分割片段保存目录")
        self.select_output_btn.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
        """)
        self.select_output_btn.clicked.connect(self.select_output_dir)
        file_layout.addWidget(self.select_output_btn, 1, 0)

        self.output_status = QLabel("请选择分割后的视频片段保存位置")
        self.output_status.setStyleSheet("color: #666; font-size: 13px; padding: 10px;")
        file_layout.addWidget(self.output_status, 1, 1, 1, 2)

        file_group.setLayout(file_layout)
        parent_layout.addWidget(file_group)
    
    def create_settings_section(self, parent_layout):
        """创建设置区域"""
        settings_group = QGroupBox("视频分割参数")
        settings_layout = QGridLayout()

        # 场景检测阈值
        threshold_label = QLabel("场景检测阈值:")
        threshold_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(threshold_label, 0, 0)

        self.threshold = QDoubleSpinBox()
        self.threshold.setRange(0.1, 10.0)
        self.threshold.setValue(1.0)
        self.threshold.setSingleStep(0.1)
        self.threshold.setToolTip("场景变化检测的敏感度，值越小越敏感")
        settings_layout.addWidget(self.threshold, 0, 1)

        threshold_tip = QLabel("(值越小越敏感)")
        threshold_tip.setStyleSheet("color: #666; font-size: 11px;")
        settings_layout.addWidget(threshold_tip, 0, 2)

        # 保留最短镜头时长
        min_duration_label = QLabel("保留最短镜头时长:")
        min_duration_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(min_duration_label, 1, 0)

        self.min_duration = QDoubleSpinBox()
        self.min_duration.setRange(0.5, 60.0)
        self.min_duration.setValue(1.0)
        self.min_duration.setSingleStep(0.1)
        self.min_duration.setSuffix(" 秒")
        self.min_duration.setToolTip("保留最短镜头时长")
        settings_layout.addWidget(self.min_duration, 1, 1)

        min_duration_tip = QLabel("(保留最短镜头)")
        min_duration_tip.setStyleSheet("color: #666; font-size: 11px;")
        settings_layout.addWidget(min_duration_tip, 1, 2)

        # 最终合成镜头时长
        save_duration_label = QLabel("最终合成镜头时长:")
        save_duration_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(save_duration_label, 2, 0)

        self.min_save_duration = QDoubleSpinBox()
        self.min_save_duration.setRange(1.0, 60.0)
        self.min_save_duration.setValue(3.0)
        self.min_save_duration.setSingleStep(0.1)
        self.min_save_duration.setSuffix(" 秒")
        self.min_save_duration.setToolTip("最终合成镜头时长")
        settings_layout.addWidget(self.min_save_duration, 2, 1)

        save_duration_tip = QLabel("(合成镜头时长)")
        save_duration_tip.setStyleSheet("color: #666; font-size: 11px;")
        settings_layout.addWidget(save_duration_tip, 2, 2)

        # 单个素材最多切割数量
        max_count_label = QLabel("单个素材最多切割数量:")
        max_count_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(max_count_label, 3, 0)

        self.max_count = QSpinBox()
        self.max_count.setRange(1, 1000)
        self.max_count.setValue(200)
        self.max_count.setSuffix(" 个")
        self.max_count.setToolTip("每个视频最多生成的片段数量")
        settings_layout.addWidget(self.max_count, 3, 1)

        # 视频缩放比例
        crop_scale_label = QLabel("视频缩放比例:")
        crop_scale_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(crop_scale_label, 4, 0)

        self.crop_scale = QDoubleSpinBox()
        self.crop_scale.setRange(0.1, 3.0)
        self.crop_scale.setValue(1.0)
        self.crop_scale.setSingleStep(0.1)
        self.crop_scale.setToolTip("视频裁剪缩放比例")
        self.crop_scale.valueChanged.connect(self.update_preview)
        settings_layout.addWidget(self.crop_scale, 4, 1)

        # 生成视频尺寸
        video_size_label = QLabel("生成视频尺寸:")
        video_size_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(video_size_label, 5, 0)

        self.video_size = QComboBox()
        self.video_size.addItems(["1080:1920 (竖屏)", "1080:1440 (方屏)"])
        self.video_size.setCurrentIndex(1)  # 默认选择1440
        self.video_size.setToolTip("选择输出视频的尺寸比例")
        self.video_size.currentTextChanged.connect(self.update_preview)
        settings_layout.addWidget(self.video_size, 5, 1)

        # 截取的字幕位置
        subtitle_pos_label = QLabel("截取的字幕位置:")
        subtitle_pos_label.setStyleSheet("font-weight: bold; color: #333;")
        settings_layout.addWidget(subtitle_pos_label, 6, 0)

        self.subtitle_position = QComboBox()
        self.subtitle_position.addItems(["字幕在下方", "字幕在上方"])
        self.subtitle_position.setCurrentIndex(0)  # 默认选择第一项
        self.subtitle_position.setToolTip("选择字幕在视频中的位置")
        self.subtitle_position.currentTextChanged.connect(self.update_preview)
        settings_layout.addWidget(self.subtitle_position, 6, 1)

        # 高级选项
        advanced_frame = QFrame()
        advanced_layout = QGridLayout()
        advanced_layout.setContentsMargins(0, 10, 0, 0)

        # 素材不足时是否合成片段
        self.is_merge = QCheckBox("素材不足时是否合成片段")
        self.is_merge.setChecked(True)
        self.is_merge.setToolTip("当短片段不足时，是否合成为长片段")
        advanced_layout.addWidget(self.is_merge, 0, 0)

        # 数据库重复检查选项
        self.enable_db_check = QCheckBox("启用数据库重复检查")
        self.enable_db_check.setChecked(True)
        self.enable_db_check.setToolTip("检查数据库中是否已处理过相同视频ID，避免重复分割")
        advanced_layout.addWidget(self.enable_db_check, 0, 1)

        # 是否删除包含人脸素材 (暂时隐藏，功能未完善)
        self.face_detection = QCheckBox("是否删除包含人脸素材")
        self.face_detection.setToolTip("*注意：此功能开启，影响切割速度，建议挂机使用")
        self.face_detection.setVisible(False)  # 隐藏人脸检测选项

        advanced_frame.setLayout(advanced_layout)
        settings_layout.addWidget(advanced_frame, 7, 0, 1, 3)

        settings_group.setLayout(settings_layout)
        parent_layout.addWidget(settings_group)

        # 创建裁剪预览区域
        self.create_preview_section(parent_layout)

    def create_preview_section(self, parent_layout):
        """创建裁剪预览区域 - 适中大小版本"""
        self.preview_group = QGroupBox("裁剪预览")
        self.preview_group.setVisible(False)  # 初始隐藏
        self.preview_group.setMaximumHeight(400)  # 增加高度
        preview_layout = QVBoxLayout()
        preview_layout.setContentsMargins(10, 10, 10, 10)  # 适中边距

        # 预览标题
        preview_title = QLabel("裁剪预览：")
        preview_title.setAlignment(Qt.AlignCenter)
        preview_title.setStyleSheet("font-weight: bold; font-size: 14px; margin: 5px 0;")
        preview_layout.addWidget(preview_title)

        # 视频预览容器 (适中大小)
        self.preview_container = QWidget()
        self.preview_container.setFixedSize(480, 300)  # 从300x180放大到480x300
        self.preview_container.setStyleSheet("background-color: #000; border: 1px solid #ccc;")

        # 视频显示标签
        self.video_preview = QLabel(self.preview_container)
        self.video_preview.setAlignment(Qt.AlignCenter)
        self.video_preview.setStyleSheet("background-color: black; color: white; font-size: 10px;")
        self.video_preview.setText("选择视频后显示预览")

        # 裁剪框覆盖层
        self.crop_overlay = QLabel(self.preview_container)
        self.crop_overlay.setStyleSheet("""
            border: 2px solid red;
            background-color: transparent;
        """)
        self.crop_overlay.setVisible(False)

        # 居中显示预览容器
        container_layout = QHBoxLayout()
        container_layout.addStretch()
        container_layout.addWidget(self.preview_container)
        container_layout.addStretch()

        preview_layout.addLayout(container_layout)

        self.preview_group.setLayout(preview_layout)
        parent_layout.addWidget(self.preview_group)

        # 连接参数变化事件
        self.crop_scale.valueChanged.connect(self.update_preview)
        self.video_size.currentTextChanged.connect(self.update_preview)
        self.subtitle_position.currentTextChanged.connect(self.update_preview)

        # 存储当前视频信息
        self.current_video_info = None

    def update_preview(self):
        """更新裁剪预览 - 完全按照原始JS逻辑"""
        print("DEBUG: update_preview 被调用")
        if not self.current_video_info:
            print("DEBUG: 没有视频信息，跳过预览更新")
            return

        try:
            # 获取当前参数 (完全对应JS变量)
            cropScale = self.crop_scale.value()  # JS: cropScale
            video_size_text = self.video_size.currentText()
            isTop = self.subtitle_position.currentText() == "字幕在下方"  # JS: isTop

            # 解析目标尺寸 (完全对应JS的height和width变量)
            if "1080:1920" in video_size_text:
                height = 1920  # JS: height
                width = 1080   # JS: width
            elif "1080:1440" in video_size_text:
                height = 1440
                width = 1080
            else:
                height = 1440
                width = 1080

            # 获取原始视频尺寸
            original_width = self.current_video_info.get('width', 1920)
            original_height = self.current_video_info.get('height', 1080)

            # 计算视频显示尺寸 (完全对应JS的video样式)
            container_width = 480
            container_height = 300

            if height == 1440:
                # JS: width: '400px', height: 'auto'
                # 原始JS: 宽度固定400px，高度按视频比例自适应
                js_video_width = 400
                js_video_height = int(400 * original_height / original_width)
                # 缩放到小预览容器，保持比例
                scale_factor = min(container_width / js_video_width, container_height / js_video_height)
                video_width = int(js_video_width * scale_factor)
                video_height = int(js_video_height * scale_factor)
                print(f"DEBUG: height=1440, JS视频尺寸: {js_video_width}x{js_video_height}, 缩放比例: {scale_factor}")
            else:
                # JS: width: 'auto', height: '540px'
                # 原始JS: 高度固定540px，宽度按视频比例自适应
                js_video_height = 540
                js_video_width = int(540 * original_width / original_height)
                # 缩放到小预览容器，保持比例
                scale_factor = min(container_width / js_video_width, container_height / js_video_height)
                video_width = int(js_video_width * scale_factor)
                video_height = int(js_video_height * scale_factor)
                print(f"DEBUG: height=1920, JS视频尺寸: {js_video_width}x{js_video_height}, 缩放比例: {scale_factor}")

            # 确保不超出容器，但保持比例
            if video_width > container_width:
                scale = container_width / video_width
                video_width = container_width
                video_height = int(video_height * scale)
            if video_height > container_height:
                scale = container_height / video_height
                video_height = container_height
                video_width = int(video_width * scale)

            # 设置视频预览位置和尺寸
            video_x = (container_width - video_width) // 2
            video_y = (container_height - video_height) // 2
            self.video_preview.setGeometry(video_x, video_y, video_width, video_height)

            # 计算裁剪框尺寸 (完全按照JS公式，然后使用相同的缩放比例)
            if height == 1920:
                # JS: width / height * (540 / cropScale)
                js_crop_width = width / height * (540 / cropScale)
                # JS: 540 / cropScale
                js_crop_height = 540 / cropScale
                # 使用与视频相同的缩放比例
                crop_width = int(js_crop_width * scale_factor)
                crop_height = int(js_crop_height * scale_factor)
            else:
                # JS: 400 / cropScale
                js_crop_width = 400 / cropScale
                # JS: height / width * (400 / cropScale)
                js_crop_height = height / width * (400 / cropScale)
                # 使用与视频相同的缩放比例
                crop_width = int(js_crop_width * scale_factor)
                crop_height = int(js_crop_height * scale_factor)

            # 计算裁剪框位置 (完全对应JS的定位逻辑)
            crop_x = video_x + (video_width - crop_width) // 2

            if isTop:
                # JS: top: 0
                crop_y = video_y
            else:
                # JS: bottom: 0
                crop_y = video_y + video_height - crop_height

            # 设置裁剪框
            self.crop_overlay.setGeometry(crop_x, crop_y, crop_width, crop_height)
            self.crop_overlay.setVisible(True)

            print(f"DEBUG: JS变量 - height={height}, width={width}, cropScale={cropScale}, isTop={isTop}")
            print(f"DEBUG: 原始视频: {original_width}x{original_height}")
            print(f"DEBUG: 视频显示: {video_width}x{video_height} at ({video_x},{video_y})")
            print(f"DEBUG: JS裁剪框公式:")
            if height == 1920:
                print(f"  js_crop_width = {width}/{height} * (540/{cropScale}) = {width/height * (540/cropScale)}")
                print(f"  js_crop_height = 540/{cropScale} = {540/cropScale}")
            else:
                print(f"  js_crop_width = 400/{cropScale} = {400/cropScale}")
                print(f"  js_crop_height = {height}/{width} * (400/{cropScale}) = {height/width * (400/cropScale)}")
            print(f"DEBUG: 最终裁剪框: {crop_width}x{crop_height} at ({crop_x},{crop_y})")

        except Exception as e:
            print(f"DEBUG: 预览更新失败: {e}")

    def create_action_section(self, parent_layout):
        """创建操作控制区域"""
        action_group = QGroupBox("分割控制")
        action_layout = QVBoxLayout()

        # 按钮区域
        button_layout = QHBoxLayout()



        # 开始分割按钮
        self.process_btn = QPushButton("🎬 开始分割")
        self.process_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)
        self.process_btn.setToolTip("开始分割视频为多个片段")
        button_layout.addWidget(self.process_btn)

        # 停止按钮
        self.stop_btn = QPushButton("⏹ 停止")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setToolTip("停止当前分割任务")
        button_layout.addWidget(self.stop_btn)

        button_layout.addStretch()
        action_layout.addLayout(button_layout)

        # 进度显示区域
        progress_layout = QHBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("")
        self.progress_label.setStyleSheet("color: #333; font-weight: bold;")
        self.progress_label.setVisible(False)
        progress_layout.addWidget(self.progress_label)

        action_layout.addLayout(progress_layout)

        action_group.setLayout(action_layout)
        parent_layout.addWidget(action_group)
    
    def create_log_section(self, parent_layout):
        """创建日志区域"""
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        parent_layout.addWidget(log_group)
    
    def show_auth_dialog(self):
        """显示授权对话框"""
        self.auth_dialog = AuthDialog()
        self.auth_dialog.auth_success.connect(self.on_auth_success)
        self.auth_dialog.show()
        self.hide()
    
    def on_auth_success(self):
        """授权成功"""
        self.is_authenticated = True
        self.show()
        self.log("授权验证成功，欢迎使用视频分割工具！")
    
    def select_videos(self):
        """选择视频文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择要分割的视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;MP4文件 (*.mp4);;所有文件 (*.*)"
        )
        if files:
            self.video_files = files
            if len(files) == 1:
                filename = os.path.basename(files[0])
                # 限制文件名显示长度，避免界面被撑开
                if len(filename) > 50:
                    display_name = filename[:47] + "..."
                else:
                    display_name = filename
                self.video_status.setText(f"✅ 已选择: {display_name}")
                self.log(f"选择视频文件: {filename}")
            else:
                self.video_status.setText(f"✅ 已选择 {len(files)} 个视频文件")
                self.log(f"选择了 {len(files)} 个视频文件")

            self.video_status.setStyleSheet("color: #4CAF50; font-size: 13px; padding: 10px; font-weight: bold;")
            self.update_button_states()

            # 加载第一个视频的预览
            self.load_video_preview(files[0])

    def load_video_preview(self, video_path):
        """加载视频预览"""
        try:
            # 获取视频信息
            from video.video_processor import VideoProcessor
            processor = VideoProcessor()
            video_info = processor.get_video_info(video_path)

            self.current_video_info = video_info

            # 显示预览区域
            self.preview_group.setVisible(True)

            # 生成并显示视频缩略图
            self.generate_video_thumbnail(video_path)

            # 更新预览
            self.update_preview()

            filename = os.path.basename(video_path)
            self.log(f"加载视频预览: {filename} ({video_info['width']}x{video_info['height']})")

        except Exception as e:
            self.log(f"加载视频预览失败: {str(e)}")
            # 使用默认视频尺寸
            self.current_video_info = {'width': 1920, 'height': 1080}
            self.preview_group.setVisible(True)
            self.video_preview.setText("视频预览\n(使用默认尺寸 1920x1080)")
            self.video_preview.setStyleSheet("color: white; font-size: 10px; background-color: #333;")
            self.update_preview()

    def generate_video_thumbnail(self, video_path):
        """生成视频缩略图"""
        try:
            import subprocess
            import tempfile

            # 创建临时缩略图文件
            temp_dir = tempfile.gettempdir()
            thumbnail_path = os.path.join(temp_dir, "video_thumbnail.jpg")

            # 使用FFmpeg生成缩略图 (取视频中间帧)
            cmd = [
                "ffmpeg",
                "-i", video_path,
                "-ss", "00:00:01",  # 跳到第1秒
                "-vframes", "1",    # 只取1帧
                "-y",               # 覆盖输出文件
                thumbnail_path
            ]

            # 在Windows下隐藏命令窗口
            kwargs = {
                'capture_output': True,
                'text': True,
                'timeout': 10,
                'encoding': 'utf-8',
                'errors': 'ignore'
            }
            if sys.platform == 'win32':
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

            result = subprocess.run(cmd, **kwargs)

            if result.returncode == 0 and os.path.exists(thumbnail_path):
                # 加载缩略图到QLabel
                from PyQt5.QtGui import QPixmap
                pixmap = QPixmap(thumbnail_path)
                if not pixmap.isNull():
                    # 设置为背景，这样裁剪框可以覆盖在上面
                    self.video_preview.setPixmap(pixmap)
                    self.video_preview.setScaledContents(True)  # 自动缩放

                # 清理临时文件
                try:
                    os.remove(thumbnail_path)
                except:
                    pass
            else:
                # 如果生成缩略图失败，显示占位文本
                self.video_preview.setText("视频预览\n(无法生成缩略图)")
                self.video_preview.setStyleSheet("color: white; font-size: 10px; background-color: #333;")

        except Exception as e:
            print(f"DEBUG: 生成缩略图失败: {e}")
            self.video_preview.setText("视频预览")
            self.video_preview.setStyleSheet("color: white; font-size: 10px; background-color: #333;")

    def select_output_dir(self):
        """选择输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择分割片段保存目录")
        if directory:
            self.output_dir = directory
            dir_name = os.path.basename(directory)
            self.output_status.setText(f"✅ 保存到: {dir_name}")
            self.output_status.setStyleSheet("color: #4CAF50; font-size: 13px; padding: 10px; font-weight: bold;")
            self.update_button_states()
            self.log(f"选择输出目录: {directory}")

    def update_button_states(self):
        """更新按钮状态"""
        has_files = bool(self.video_files)
        has_output = bool(self.output_dir)

        # 分割按钮：需要有视频文件和输出目录
        self.process_btn.setEnabled(has_files and has_output)

    def preview_analysis(self):
        """预览分析视频"""
        if not self.video_files:
            return

        self.log("开始分析视频场景...")
        QMessageBox.information(
            self, "预览分析",
            f"将分析 {len(self.video_files)} 个视频文件的场景变化\n"
            f"检测阈值: {self.threshold.value()}\n"
            f"最短片段: {self.min_duration.value()}秒\n"
            f"最大数量: {self.max_count.value()}个"
        )
    
    def start_processing(self):
        """开始分割视频"""
        if not self.video_files or not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择视频文件和输出目录")
            return

        # 检查FFmpeg是否可用
        try:
            kwargs = {'capture_output': True, 'timeout': 5}
            if sys.platform == 'win32':
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            subprocess.run(['ffmpeg', '-version'], **kwargs)
        except (FileNotFoundError, subprocess.TimeoutExpired):
            QMessageBox.critical(
                self, "错误",
                "未找到FFmpeg！\n\n"
                "请确保已安装FFmpeg并添加到PATH环境变量中。\n"
                "下载地址: https://ffmpeg.org/download.html"
            )
            return

        # 获取设置参数
        settings = {
            'threshold': self.threshold.value(),
            'min_duration': self.min_duration.value(),
            'min_save_duration': self.min_save_duration.value(),
            'max_count': self.max_count.value(),
            'crop_scale': self.crop_scale.value(),
            'video_size': self.video_size.currentText(),
            'is_top': self.subtitle_position.currentText() == "字幕在下方",
            'is_merge': self.is_merge.isChecked(),
            'face_detection': self.face_detection.isChecked()
        }

        self.log(f"🚀 开始分割 {len(self.video_files)} 个视频文件...")
        if self.db_connected:
            self.log("� 已启用数据库重复检查功能")

        # 更新界面状态
        self.process_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建并启动处理线程，传递数据库管理器
        self.processing_thread = VideoProcessingThread(
            self.video_files,
            self.output_dir,
            settings,
            self.db_manager if self.db_connected else None
        )
        self.processing_thread.progress_updated.connect(self.on_progress_updated)
        self.processing_thread.processing_finished.connect(self.on_processing_finished)
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理"""
        if hasattr(self, 'processing_thread') and self.processing_thread.isRunning():
            self.log("⏹ 正在停止分割任务...")
            self.processing_thread.cancel()
            self.processing_thread.wait(3000)  # 等待最多3秒

        self.reset_ui_state()
        self.log("🛑 分割任务已停止")
        QMessageBox.information(self, "已停止", "分割任务已停止")

    def on_progress_updated(self, progress, message):
        """处理进度更新"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)
        self.log(message)

    def on_processing_finished(self, success, message):
        """处理完成"""
        self.reset_ui_state()
        self.log(message)

        if success:
            QMessageBox.information(self, "分割完成", message)
            # 询问是否打开输出目录
            reply = QMessageBox.question(
                self, "打开目录",
                "是否要打开输出目录查看分割结果？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.open_output_directory()
        else:
            QMessageBox.critical(self, "分割失败", message)

    def open_output_directory(self):
        """打开输出目录"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.output_dir)
            elif os.name == 'posix':  # macOS and Linux
                kwargs = {}
                if sys.platform == 'win32':
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', self.output_dir], **kwargs)
        except Exception as e:
            self.log(f"无法打开目录: {str(e)}")

    def reset_ui_state(self):
        """重置界面状态"""
        self.process_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.update_button_states()
    


    def create_log_section(self, parent_layout):
        """创建日志区域"""
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)
        parent_layout.addWidget(log_group)

    def log(self, message):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # 设置应用信息
    app.setApplicationName("闪灵猫-分割")
    app.setApplicationVersion("2.0.4")
    app.setOrganizationName("智能视频处理工具")

    # 创建主窗口
    main_window = MainWindow()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()





